<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <!-- 1、前列腺局部病灶评估 -->
      <el-form-item prop="p1" label="1、前列腺局部病灶评估">
        <el-radio-group v-model="form.p1">
          <el-radio label="有异常" border/>
          <el-radio label="无异常" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 病灶位置 -->
      <el-form-item v-if="form.p1 === '有异常'" prop="p1Location" label="（1）病灶位置">
        <el-checkbox-group v-model="form.p1Location">
          <el-checkbox label="前列腺外周带" border />
          <el-checkbox label="中央带" border />
          <el-checkbox label="移行带" border />
          <el-checkbox label="累及前列腺包膜外" border />
          <el-checkbox label="其他" border />
        </el-checkbox-group>
        <el-input
          v-if="form.p1Location.includes('其他')"
          v-model="form.p1LocationOther"
          placeholder="请填写其他位置"
          maxlength="50"
          show-word-limit
          class="p1-location-other"
        />
      </el-form-item>

      <!-- PSMA摄取特征 -->
      <el-form-item v-if="form.p1 === '有异常'" prop="p1PsmaUptake" label="（2）PSMA 摄取特征">
        <el-radio-group v-model="form.p1PsmaUptake" class="p1-psma-group">
          <el-radio label="高摄取" border />
          <el-radio label="中等摄取" border />
          <el-radio label="低摄取" border />
        </el-radio-group>
      </el-form-item>

      <!-- 病灶数量 -->
      <el-form-item v-if="form.p1 === '有异常'" prop="p1Number" label="（3）病灶数量">
        <el-radio-group v-model="form.p1Number" class="p1-number-group">
          <el-radio label="单发病灶" border />
          <el-radio label="多发病灶" border />
        </el-radio-group>
      </el-form-item>

      <!-- 病灶最大径 -->
      <el-form-item v-if="form.p1 === '有异常'" prop="p1MaxDiameter" label="（4）病灶最大径">
        <el-input
          v-model="form.p1MaxDiameter"
          placeholder="请输入病灶最大径"
          maxlength="10"
          show-word-limit
          style="width: 200px"
        />
        <span style="margin-left: 5px;">cm</span>
      </el-form-item>

      <!-- 2、区域淋巴结转移评估 -->
      <el-form-item prop="p2" label="2、区域淋巴结转移评估">
        <el-radio-group v-model="form.p2">
          <el-radio label="有转移" border/>
          <el-radio label="无转移" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 转移淋巴结部位 -->
      <el-form-item v-if="form.p2 === '有转移'" prop="p2Location" label="（1）转移淋巴结部位（可多选）">
        <el-checkbox-group v-model="form.p2Location">
          <el-checkbox label="盆腔淋巴结（髂内）" border />
          <el-checkbox label="盆腔淋巴结（髂外）" border />
          <el-checkbox label="盆腔淋巴结（闭孔区）" border />
          <el-checkbox label="腹主动脉旁淋巴结" border />
          <el-checkbox label="其他" border />
        </el-checkbox-group>
        <el-input
          v-if="form.p2Location.includes('其他')"
          v-model="form.p2LocationOther"
          placeholder="请填写其他部位"
          maxlength="50"
          show-word-limit
          class="p2-location-other"
        />
      </el-form-item>

      <!-- 转移淋巴结数量 -->
      <el-form-item v-if="form.p2 === '有转移'" prop="p2Number" label="（2）转移淋巴结数量">
        <el-input
          v-model="form.p2Number"
          placeholder="请输入数量"
          maxlength="10"
          show-word-limit
          style="width: 200px"
        />
        <span style="margin-left: 5px;">枚</span>
      </el-form-item>

      <!-- 最大转移淋巴结径线 -->
      <el-form-item v-if="form.p2 === '有转移'" prop="p2MaxDiameter" label="（3）最大转移淋巴结径线">
        <el-input
          v-model="form.p2MaxDiameter"
          placeholder="请输入径线"
          maxlength="10"
          show-word-limit
          style="width: 200px"
        />
        <span style="margin-left: 5px;">cm</span>
      </el-form-item>

      <!-- 淋巴结PSMA摄取特征 -->
      <el-form-item v-if="form.p2 === '有转移'" prop="p2PsmaUptake" label="（4）PSMA 摄取特征">
        <el-radio-group v-model="form.p2PsmaUptake" class="p2-psma-group">
          <el-radio label="高摄取" border />
          <el-radio label="中等摄取" border />
          <el-radio label="低摄取" border />
        </el-radio-group>
      </el-form-item>

      <!-- 3、远处转移评估 -->
      <el-form-item prop="p3" label="3、远处转移评估">
        <el-radio-group v-model="form.p3">
          <el-radio label="有转移" border/>
          <el-radio label="无转移" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 转移部位 -->
      <el-form-item v-if="form.p3 === '有转移'" prop="p3Location" label="（1）转移部位（可多选）">
        <el-checkbox-group v-model="form.p3Location">
          <el-checkbox label="骨转移（腰背部）" border />
          <el-checkbox label="骨转移（髋部）" border />
          <el-checkbox label="骨转移（肋骨）" border />
          <el-checkbox label="肺转移" border />
          <el-checkbox label="肝转移" border />
          <el-checkbox label="其他器官转移" border />
          <el-checkbox label="其他" border />
        </el-checkbox-group>
        <el-input
          v-if="form.p3Location.includes('其他器官转移') || form.p3Location.includes('其他')"
          v-model="form.p3LocationOther"
          placeholder="请填写其他转移部位"
          maxlength="50"
          show-word-limit
          class="p3-location-other"
        />
      </el-form-item>

      <!-- 转移灶数量 -->
      <el-form-item v-if="form.p3 === '有转移'" prop="p3Number" label="（2）转移灶数量">
        <el-radio-group v-model="form.p3Number" class="p3-number-group">
          <el-radio label="单灶转移" border />
          <el-radio label="多灶转移" border />
          <el-radio label="弥漫性转移" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.p3Number === '其他'"
          v-model="form.p3NumberOther"
          placeholder="请填写其他情况"
          maxlength="50"
          show-word-limit
          class="p3-number-other"
        />
      </el-form-item>

      <!-- 骨转移特征 -->
      <el-form-item v-if="form.p3 === '有转移' && (form.p3Location.includes('骨转移（腰背部）') || form.p3Location.includes('骨转移（髋部）') || form.p3Location.includes('骨转移（肋骨）'))" prop="p3BoneFeature" label="（3）骨转移特征（若有）">
        <el-radio-group v-model="form.p3BoneFeature" class="p3-bone-group">
          <el-radio label="溶骨性破坏" border />
          <el-radio label="成骨性破坏" border />
          <el-radio label="混合性破坏" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.p3BoneFeature === '其他'"
          v-model="form.p3BoneFeatureOther"
          placeholder="请填写其他特征"
          maxlength="50"
          show-word-limit
          class="p3-bone-other"
        />
      </el-form-item>

      <!-- 远处转移PSMA摄取特征 -->
      <el-form-item v-if="form.p3 === '有转移'" prop="p3PsmaUptake" label="（4）PSMA 摄取特征（转移灶）">
        <el-radio-group v-model="form.p3PsmaUptake" class="p3-psma-group">
          <el-radio label="高摄取" border />
          <el-radio label="中等摄取" border />
          <el-radio label="低摄取" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.p3PsmaUptake === '其他'"
          v-model="form.p3PsmaUptakeOther"
          placeholder="请填写其他摄取特征"
          maxlength="50"
          show-word-limit
          class="p3-psma-other"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0159',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      // 初始化所有可能的字段
      const fields = [
        'p1Location', 'p1LocationOther', 'p1PsmaUptake', 'p1Number', 'p1MaxDiameter',
        'p2Location', 'p2LocationOther', 'p2Number', 'p2MaxDiameter', 'p2PsmaUptake',
        'p3Location', 'p3LocationOther', 'p3Number', 'p3NumberOther', 'p3BoneFeature',
        'p3BoneFeatureOther', 'p3PsmaUptake', 'p3PsmaUptakeOther'
      ];

      fields.forEach(field => {
        if (this.form[field] === undefined) {
          if (field.includes('Location') && !field.includes('Other')) {
            this.$set(this.form, field, []);
          } else {
            this.$set(this.form, field, '');
          }
        }
      });
    }
  },
  data() {
    // 验证函数
    const validateP1Location = (rule, value, callback) => {
      if (this.form.p1 === '有异常' && (!value || value.length === 0)) {
        callback(new Error('请选择病灶位置'));
      } else {
        callback();
      }
    };

    const validateP1LocationOther = (rule, value, callback) => {
      if (this.form.p1 === '有异常' && this.form.p1Location.includes('其他') && !value) {
        callback(new Error('请填写其他位置'));
      } else {
        callback();
      }
    };

    const validateP1PsmaUptake = (rule, value, callback) => {
      if (this.form.p1 === '有异常' && !value) {
        callback(new Error('请选择PSMA摄取特征'));
      } else {
        callback();
      }
    };

    const validateP1Number = (rule, value, callback) => {
      if (this.form.p1 === '有异常' && !value) {
        callback(new Error('请选择病灶数量'));
      } else {
        callback();
      }
    };

    const validateP1MaxDiameter = (rule, value, callback) => {
      if (this.form.p1 === '有异常' && !value) {
        callback(new Error('请输入病灶最大径'));
      } else {
        callback();
      }
    };

    const validateP2Location = (rule, value, callback) => {
      if (this.form.p2 === '有转移' && (!value || value.length === 0)) {
        callback(new Error('请选择转移淋巴结部位'));
      } else {
        callback();
      }
    };

    const validateP2LocationOther = (rule, value, callback) => {
      if (this.form.p2 === '有转移' && this.form.p2Location.includes('其他') && !value) {
        callback(new Error('请填写其他部位'));
      } else {
        callback();
      }
    };

    const validateP2Number = (rule, value, callback) => {
      if (this.form.p2 === '有转移' && !value) {
        callback(new Error('请输入转移淋巴结数量'));
      } else {
        callback();
      }
    };

    const validateP2MaxDiameter = (rule, value, callback) => {
      if (this.form.p2 === '有转移' && !value) {
        callback(new Error('请输入最大转移淋巴结径线'));
      } else {
        callback();
      }
    };

    const validateP2PsmaUptake = (rule, value, callback) => {
      if (this.form.p2 === '有转移' && !value) {
        callback(new Error('请选择PSMA摄取特征'));
      } else {
        callback();
      }
    };

    const validateP3Location = (rule, value, callback) => {
      if (this.form.p3 === '有转移' && (!value || value.length === 0)) {
        callback(new Error('请选择转移部位'));
      } else {
        callback();
      }
    };

    const validateP3LocationOther = (rule, value, callback) => {
      if (this.form.p3 === '有转移' && (this.form.p3Location.includes('其他器官转移') || this.form.p3Location.includes('其他')) && !value) {
        callback(new Error('请填写其他转移部位'));
      } else {
        callback();
      }
    };

    const validateP3Number = (rule, value, callback) => {
      if (this.form.p3 === '有转移' && !value) {
        callback(new Error('请选择转移灶数量'));
      } else {
        callback();
      }
    };

    const validateP3NumberOther = (rule, value, callback) => {
      if (this.form.p3 === '有转移' && this.form.p3Number === '其他' && !value) {
        callback(new Error('请填写其他情况'));
      } else {
        callback();
      }
    };

    const validateP3BoneFeatureOther = (rule, value, callback) => {
      if (this.form.p3 === '有转移' && this.form.p3BoneFeature === '其他' && !value) {
        callback(new Error('请填写其他特征'));
      } else {
        callback();
      }
    };

    const validateP3PsmaUptake = (rule, value, callback) => {
      if (this.form.p3 === '有转移' && !value) {
        callback(new Error('请选择PSMA摄取特征'));
      } else {
        callback();
      }
    };

    const validateP3PsmaUptakeOther = (rule, value, callback) => {
      if (this.form.p3 === '有转移' && this.form.p3PsmaUptake === '其他' && !value) {
        callback(new Error('请填写其他摄取特征'));
      } else {
        callback();
      }
    };

    return {
      form: {
        p1: undefined,
        p1Location: [],
        p1LocationOther: '',
        p1PsmaUptake: '',
        p1Number: '',
        p1MaxDiameter: '',
        p2: undefined,
        p2Location: [],
        p2LocationOther: '',
        p2Number: '',
        p2MaxDiameter: '',
        p2PsmaUptake: '',
        p3: undefined,
        p3Location: [],
        p3LocationOther: '',
        p3Number: '',
        p3NumberOther: '',
        p3BoneFeature: '',
        p3BoneFeatureOther: '',
        p3PsmaUptake: '',
        p3PsmaUptakeOther: '',
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p1Location: [{ validator: validateP1Location, trigger: 'change' }],
        p1LocationOther: [{ validator: validateP1LocationOther, trigger: 'blur' }],
        p1PsmaUptake: [{ validator: validateP1PsmaUptake, trigger: 'change' }],
        p1Number: [{ validator: validateP1Number, trigger: 'change' }],
        p1MaxDiameter: [{ validator: validateP1MaxDiameter, trigger: 'blur' }],
        p2: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p2Location: [{ validator: validateP2Location, trigger: 'change' }],
        p2LocationOther: [{ validator: validateP2LocationOther, trigger: 'blur' }],
        p2Number: [{ validator: validateP2Number, trigger: 'blur' }],
        p2MaxDiameter: [{ validator: validateP2MaxDiameter, trigger: 'blur' }],
        p2PsmaUptake: [{ validator: validateP2PsmaUptake, trigger: 'change' }],
        p3: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p3Location: [{ validator: validateP3Location, trigger: 'change' }],
        p3LocationOther: [{ validator: validateP3LocationOther, trigger: 'blur' }],
        p3Number: [{ validator: validateP3Number, trigger: 'change' }],
        p3NumberOther: [{ validator: validateP3NumberOther, trigger: 'blur' }],
        p3BoneFeatureOther: [{ validator: validateP3BoneFeatureOther, trigger: 'blur' }],
        p3PsmaUptake: [{ validator: validateP3PsmaUptake, trigger: 'change' }],
        p3PsmaUptakeOther: [{ validator: validateP3PsmaUptakeOther, trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
  watch: {
    'form.p1'(val) {
      if (val !== '有异常') {
        this.form.p1Location = [];
        this.form.p1LocationOther = '';
        this.form.p1PsmaUptake = '';
        this.form.p1Number = '';
        this.form.p1MaxDiameter = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p1Location', 'p1LocationOther', 'p1PsmaUptake', 'p1Number', 'p1MaxDiameter']);
        }
      }
    },
    'form.p1Location'(val) {
      if (!val.includes('其他')) {
        this.form.p1LocationOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p1LocationOther');
        }
      }
    },
    'form.p2'(val) {
      if (val !== '有转移') {
        this.form.p2Location = [];
        this.form.p2LocationOther = '';
        this.form.p2Number = '';
        this.form.p2MaxDiameter = '';
        this.form.p2PsmaUptake = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p2Location', 'p2LocationOther', 'p2Number', 'p2MaxDiameter', 'p2PsmaUptake']);
        }
      }
    },
    'form.p2Location'(val) {
      if (!val.includes('其他')) {
        this.form.p2LocationOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p2LocationOther');
        }
      }
    },
    'form.p3'(val) {
      if (val !== '有转移') {
        this.form.p3Location = [];
        this.form.p3LocationOther = '';
        this.form.p3Number = '';
        this.form.p3NumberOther = '';
        this.form.p3BoneFeature = '';
        this.form.p3BoneFeatureOther = '';
        this.form.p3PsmaUptake = '';
        this.form.p3PsmaUptakeOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p3Location', 'p3LocationOther', 'p3Number', 'p3NumberOther', 'p3BoneFeature', 'p3BoneFeatureOther', 'p3PsmaUptake', 'p3PsmaUptakeOther']);
        }
      }
    },
    'form.p3Location'(val) {
      if (!val.includes('其他器官转移') && !val.includes('其他')) {
        this.form.p3LocationOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p3LocationOther');
        }
      }
    },
    'form.p3Number'(val) {
      if (val !== '其他') {
        this.form.p3NumberOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p3NumberOther');
        }
      }
    },
    'form.p3BoneFeature'(val) {
      if (val !== '其他') {
        this.form.p3BoneFeatureOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p3BoneFeatureOther');
        }
      }
    },
    'form.p3PsmaUptake'(val) {
      if (val !== '其他') {
        this.form.p3PsmaUptakeOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p3PsmaUptakeOther');
        }
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"p1",
      label:"1、前列腺局部病灶评估（有 / 无异常）"
    }, {
      key:"p2",
      label:"2、区域淋巴结转移评估（有 / 无转移）"
    }, {
      key:"p3",
      label:"3、远处转移评估（有 / 无转移）"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-checkbox--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
  margin-right: 10px;
  margin-bottom: 10px;
}

::v-deep .el-checkbox__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-checkbox__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
}

::v-deep .el-checkbox-group {
  font-size: 0;
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

::v-deep .el-checkbox {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

.p1-location-other,
.p2-location-other,
.p3-location-other,
.p3-number-other,
.p3-bone-other,
.p3-psma-other {
  width: 300px;
  margin-left: 10px;
  margin-top: 10px;
}

.p1-psma-group,
.p1-number-group,
.p2-psma-group,
.p3-number-group,
.p3-bone-group,
.p3-psma-group {
  display: inline-flex;
  align-items: center;
}
</style>
